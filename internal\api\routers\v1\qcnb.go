package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterNBQCRouter(router *gin.RouterGroup) {

	//qcqmb
	router.POST("/getProductionDefectSummary", controllers.QIP.GetProductionDefectSummary)
	router.GET("/getDepName", controllers.QIP.GetBDepartment)
	router.POST("/GetLeanProcessDepartments", controllers.QIP.GetLeanProcessDepartments)

	//qc report
	router.POST("/getTotalErrlean", controllers.QC_Report.GetTotalErrLeanInDay)
	router.POST("/getDPPMErrlean", controllers.QC_Report.GetDPPMErrleaninday)
	router.POST("/getDPPMErrAllLean", controllers.QC_Report.GetDPPMErrAllLeanInDay)
	router.POST("/getDailyDefectDPPMReport", controllers.QC_Report.GetDaily_Defect_DPPM_Report)
	router.POST("/getDailyDPPMByLeanReport", controllers.QC_Report.DailyDPPMByLeanReport)

	//Defect Qty by Process
	router.POST("/GetDefectByHour", controllers.Defect_Qty_By_Process.GetDefectByHour)
	
	// Defect Qty by Line
	router.POST("/GetDefectsQtyByLine", controllers.Defect_Qty_By_Line.GetHourlyDefectsByLean)

	//Login
	router.POST("/Login", controllers.User_Login.Login)

	router.POST("/xuatexcel", controllers.QIP.XuatExcel)

	router.POST("/GetExcelData", controllers.QIP.ExportExcelHandler)
}
