// encryption.go
package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"errors"
	"fmt" 
	"log"

	// "os"

	"github.com/joho/godotenv"
)

// init loads environment variables from .env
func init() {
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found or failed to load")
	}
}

// deriveKeyAndIV simulates OpenSSL's EVP_BytesToKey to generate key and IV from passphrase and salt
func deriveKeyAndIV(password, salt []byte) (key, iv []byte) {
	var prev []byte
	hasher := md5.New()
	// AES-256 key length: 32 bytes, IV length: 16 bytes
	for len(key) < 32+16 {
		hasher.Reset()
		if prev != nil {
			hasher.Write(prev)
		}
		hasher.Write(password)
		hasher.Write(salt)
		prev = hasher.Sum(nil)
		key = append(key, prev...)
	}
	return key[:32], key[32:48]
}

// DecryptAES decrypts a Base64-encoded string encrypted by CryptoJS.AES (OpenSSL-compatible)
func DecryptAES(encryptedBase64 string) (string, error) {
	// Load secret key
	secret := SecretKey
	if secret == "" {
		return "", fmt.Errorf("environment variable SECRET_KEY is not set")
	}

	// Decode Base64
	data, err := base64.StdEncoding.DecodeString(encryptedBase64)
	if err != nil {
		return "", fmt.Errorf("base64 decode failed: %w", err)
	}

	// Check Salted__ header
	if len(data) < 16 || string(data[:8]) != "Salted__" {
		return "", errors.New("invalid encrypted data: missing Salted__ header")
	}
	// Extract salt
	salt := data[8:16]
	key, iv := deriveKeyAndIV([]byte(secret), salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}
	mode := cipher.NewCBCDecrypter(block, iv)

	// Decrypt
	ciphertext := data[16:]
	if len(ciphertext)%aes.BlockSize != 0 {
		return "", errors.New("ciphertext is not a multiple of the block size")
	}
	mode.CryptBlocks(ciphertext, ciphertext)

	// Unpad PKCS7
	paddingLen := int(ciphertext[len(ciphertext)-1])
	if paddingLen < 1 || paddingLen > aes.BlockSize {
		return "", errors.New("invalid padding size")
	}
	plaintext := ciphertext[:len(ciphertext)-paddingLen]

	return string(plaintext), nil
}
