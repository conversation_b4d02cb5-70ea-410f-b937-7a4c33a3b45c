package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type Defect_Qty_By_Process_Service struct {
	*BaseService
}

var Defect_Qty_By_Process = &Defect_Qty_By_Process_Service{}

func (s *Defect_Qty_By_Process_Service) GetDefectByHourByGXLB(req *types.BDepartment) ([]types.GXLB_A_DefectByHour, error) {
	var BDep []types.GXLB_A_DefectByHour

	db, err := database.TB_ERP_Connection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	query := fmt.Sprintf(`
WITH DefectsDetails AS (
    SELECT 
        BD.GXLB,
        CASE 
            WHEN RIGHT(BD.DepName, 1) = 'G' THEN 'ASSEMBLY'
            WHEN RIGHT(BD.DepName, 1) = 'C' THEN 'CUTTING'
            WHEN RIGHT(BD.DepName, 1) = 'M' THEN 'STITCHING'
            ELSE 'UNKNOWN'
        END AS CongDoan,
        QCMAYYSS.YWSM,
        QCMAYYSS.ZWSM,
        DATEADD(HOUR, DATEPART(HOUR, QCR.USERDATE), CONVERT(DATETIME, CAST(QCR.USERDATE AS DATE))) AS HourBlock,
        SUM(QCRD.Qty) AS Total_Defects
    FROM QCR
    LEFT JOIN QCRD ON QCR.ProNo = QCRD.ProNo
    LEFT JOIN BDepartment BD ON BD.ID = QCR.DepNO
    LEFT JOIN QCMAYY ON QCMAYY.YYBH = QCRD.YYBH
    LEFT JOIN QCMAYYSS on QCMAYYSS.ID = QCMAYY.MajorErrorID
    WHERE BD.GXLB = ?
      AND QCMAYY.GSBH = 'TBA'
      AND BD.DepName LIKE ?
    AND QCR.USERDATE >= CAST(? + ' 00:00:00' AS DATETIME)
    AND QCR.USERDATE <= CAST(? + ' 23:59:59.997' AS DATETIME)
    GROUP BY 
        BD.GXLB,
        CASE 
            WHEN RIGHT(BD.DepName, 1) = 'G' THEN 'ASSEMBLY'
            WHEN RIGHT(BD.DepName, 1) = 'C' THEN 'CUTTING'
            WHEN RIGHT(BD.DepName, 1) = 'M' THEN 'STITCHING'
            ELSE 'UNKNOWN'
        END,
        QCMAYYSS.YWSM, 
        QCMAYYSS.ZWSM,
        DATEADD(HOUR, DATEPART(HOUR, QCR.USERDATE), CONVERT(DATETIME, CAST(QCR.USERDATE AS DATE)))
),
Aggregated AS (
    SELECT 
        GXLB,
        CongDoan,
        YWSM,
        ZWSM,
        HourBlock,
        SUM(Total_Defects) AS Total_Defects
    FROM DefectsDetails
    GROUP BY 
        GXLB,
        CongDoan,
        YWSM,
        ZWSM,
        HourBlock
),
Ranked AS (
    SELECT
      *,
      ROW_NUMBER() OVER(
        PARTITION BY HourBlock
        ORDER BY Total_Defects DESC
      ) AS rn
    FROM Aggregated
),
Labelled AS (
    SELECT
      GXLB,
      CongDoan,
      CASE WHEN rn <= 9 THEN YWSM ELSE N'Other' END AS YWSM,
      CASE WHEN rn <= 9 THEN ZWSM ELSE N'Lỗi Khác' END AS ZWSM,
      HourBlock,
      Total_Defects
    FROM Ranked
)
SELECT
    GXLB,
    CongDoan,
    YWSM,
    ZWSM,
    HourBlock AS USERDATE,
    SUM(Total_Defects) AS Total_Defects
FROM Labelled
GROUP BY
    GXLB,
    CongDoan,
    YWSM,
    ZWSM,
    HourBlock
ORDER BY
    HourBlock,
    CASE WHEN YWSM = N'Other' THEN 10 ELSE 0 END,
    SUM(Total_Defects) DESC;`)

	err = db.Raw(query, req.GXLB, req.DepName, req.Date, req.Date).Scan(&BDep).Error
	if err != nil {
		fmt.Println("Query execution error:", err)
		return nil, err
	}

	return BDep, nil
}
