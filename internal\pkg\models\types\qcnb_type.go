package types

type ProductionDefectSummaryRequest struct {
	DepName   string `json:"DepName"`
	StartDate string `json:"StartDate"`
	EndDate   string `json:"EndDate"`
}

type ProductionDefectSummary struct {
	DepNo         string           `json:"DepNo"`
	LeanName      string           `json:"LeanName"`
	TimeRange     string           `json:"TimeRange"`
	Pro_Quantity  int              `json:"Pro_Quantity"`
	Total_Defects int              `json:"Total_Defects"`
	Defect_Rate   float64          `json:"Defect_Rate"`
	Data          []DefectAnalysis `json:"data" gorm:"-"`
}

type DefectAnalysis struct {
	DepNo                 string  `json:"DepNo"`
	YWSM                  string  `json:"YWSM"`
	ZWSM                  string  `json:"ZWSM"`
	Total_Defects         int     `json:"Total_Defects"`
	Defect_Percentage     float64 `json:"Defect_Percentage"`
	Cumulative_Percentage float64 `json:"Cumulative_Percentage"`
	Item                  int     `json:"Item"`
}

type BDepartment struct {
	DepName  string `json:"DepName"`
	GXLB     string `json:"GXLB"`
	Date     string `json:"Date"`
}

type GXLB_A_DefectByHour struct {
	GXLB          string `json:"GXLB"`
	CongDoan      string `json:"CongDoan"`
	YWSM          string `json:"YWSM"`
	ZWSM          string `json:"ZWSM"`
	USERDATE      string `json:"USERDATE"`
	Total_Defects int    `json:"Total_Defects"`
}

type HourlyDefectsByLean struct {
	GXLB          string `json:"GXLB"`
	YWSM          string `json:"YWSM"`
	ZWSM          string `json:"ZWSM"`
	DepName       string `json:"DepName"`
	USERDATE      string `json:"USERDATE"`
	Total_Defects int    `json:"Total_Defects"`
}

type HourlyDefects struct {
	GXLB    string `json:"GXLB"`
	DepName string `json:"DepName"`
	Date    string `json:"Date"`
}
