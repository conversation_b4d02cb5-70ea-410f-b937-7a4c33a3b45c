package controllers

import (
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type Defect_Qty_By_Process_Controller struct {
	*BaseController
}

var Defect_Qty_By_Process = &Defect_Qty_By_Process_Controller{}

func (c *Defect_Qty_By_Process_Controller) GetDefectByHour(ctx *gin.Context) {
	var requestParams types.BDepartment
	if err := ctx.ShouldBindJSON(&requestParams); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body: "+err.Error())
		return
	}

	result, err := services.Defect_Qty_By_Process.GetDefectByHourByGXLB(&requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.<PERSON>rror())
		return
	}
	response.OkWithData(ctx, result)

}
