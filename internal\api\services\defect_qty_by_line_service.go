package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type Defect_Qty_By_Line_Service struct {
	*BaseService
}

var Defect_Qty_By_Line = &Defect_Qty_By_Line_Service{}

func (s *Defect_Qty_By_Line_Service) GetDefectsQtyByLine(req *types.HourlyDefects) ([]types.HourlyDefectsByLean, error) {
	var HourlyDefect []types.HourlyDefectsByLean

	// Kết nối DB
	db, err := database.TB_ERP_Connection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	query := `
		
WITH DefectsDetails AS (
    SELECT 
        CASE 
            WHEN BD.DepName LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 '
            WHEN BD.DepName LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 '
            WHEN BD.DepName LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 '
            WHEN BD.DepName LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 '
            WHEN BD.DepName LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 '
            WHEN BD.DepName = 'A7_GCD-01' THEN 'A7 LEAN1' 
			WHEN BD.DepName = 'A7_GCD-02' THEN 'A7 LEAN2' 
			WHEN BD.DepName = 'A7_GCD-03' THEN 'A7 LEAN3' 
			WHEN BD.DepName = 'A7_GCD-04' THEN 'A7 LEAN4' 
			WHEN BD.DepName = 'A7_GCD-05' THEN 'A7 LEAN5' 
            ELSE BD.DepName
        END
        +
        CASE 
            WHEN RIGHT(BD.DepName,1)='G' THEN 'ASSEMBLY'
            WHEN RIGHT(BD.DepName,1)='C' THEN 'CUTTING'
            WHEN RIGHT(BD.DepName,1)='M' THEN 'STITCHING'
            WHEN BD.DepName like '%GCD%' THEN ' STOCKFITTING'
            ELSE ''
        END AS GXLB,
		QCMAYYSS.YWSM ,
        QCMAYYSS.ZWSM       ,    
        BD.DepName,
        CAST(QCR.USERDATE AS DATE) AS USERDATE,
        SUM(QCRD.Qty) AS Total_Defects
    FROM QCR
    LEFT JOIN QCRD      ON QCR.ProNo   = QCRD.ProNo
    LEFT JOIN BDepartment BD ON BD.ID = QCR.DepNO
	LEFT JOIN QCMAYY    ON QCMAYY.YYBH   = QCRD.YYBH
    LEFT JOIN QCMAYYSS on QCMAYYSS.ID = QCMAYY.MajorErrorID
    WHERE BD.DepName LIKE ? 
      AND BD.GXLB = ?
      AND QCMAYY.GSBH = 'TBA'
      AND CAST(QCR.USERDATE AS DATE) = ?
    GROUP BY 
        BD.DepName, 
        QCMAYYSS.YWSM,
        QCMAYYSS.ZWSM,
        CAST(QCR.USERDATE AS DATE)
),
GroupDetails AS ( 
    SELECT  
        GXLB,
        YWSM,
        ZWSM, 
        DepName,
        USERDATE,
        SUM(Total_Defects) AS Total_Defects
    FROM DefectsDetails
    GROUP BY 
        GXLB, YWSM, ZWSM, DepName, USERDATE
),
Ranked AS ( 
    SELECT
        *,
        ROW_NUMBER() OVER(
            PARTITION BY GXLB, DepName, USERDATE
            ORDER BY Total_Defects DESC
        ) AS rn
    FROM GroupDetails
),
Labelled AS ( 
    SELECT
        GXLB,
        DepName,
        USERDATE,
        CASE WHEN rn <= 9 THEN YWSM ELSE N'Other' END AS YWSM,
        CASE WHEN rn <= 9 THEN ZWSM ELSE N'Lỗi Khác' END AS ZWSM,
        Total_Defects
    FROM Ranked
),
Final AS ( 
    SELECT
        GXLB,
        DepName,
        USERDATE,
        YWSM,
        ZWSM,
        SUM(Total_Defects) AS Total_Defects
    FROM Labelled
    GROUP BY
        GXLB, DepName, USERDATE, YWSM, ZWSM
)
SELECT
    GXLB,
    YWSM,
    ZWSM,
    DepName,
    CONVERT(VARCHAR(10), USERDATE, 120) AS USERDATE,
    Total_Defects
FROM Final
ORDER BY 
    GXLB,
    DepName,
    USERDATE,
    CASE WHEN YWSM = N'Other' THEN 1 ELSE 0 END,
    Total_Defects DESC;
	`

	// Thực hiện truy vấn
	err = db.Raw(query,req.DepName, req.GXLB, req.Date).Scan(&HourlyDefect).Error
	if err != nil {
		fmt.Println("Query execution error:", err)
		return nil, err
	}

	return HourlyDefect, nil
}
