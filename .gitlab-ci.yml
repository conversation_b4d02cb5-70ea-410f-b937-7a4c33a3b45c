
stages:
  - build
  - deploy
variables:
  DOCKER_DRIVER: overlay2
  SERVICE_NAME: "qip_api"
  PORT_MAPPING: '8084:8084'         # Define the port mapping as a variable 
  TAR_FILE: "${SERVICE_NAME}.tar" # 定义 tar 文件名变量
  SECRET_KEY: '$SECRET_KEY'
# 定义构建阶段
build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker info
  script:
    # 构建 Docker 镜像并保存为 tar 文件
    - docker build -t ${SERVICE_NAME}:latest .
    - docker save -o ${TAR_FILE} ${SERVICE_NAME}:latest
  cache:
    # 缓存 tar 文件
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - ${TAR_FILE}
  only:
    - main # 仅在 main 分支上执行
# 定义部署阶段
deploy:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  dependencies:
    - build # 依赖于构建阶段
  before_script:
      - apk add --update --no-cache openssh-client
      - eval $(ssh-agent -s)
      - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
      - mkdir -p ~/.ssh
      - chmod 700 ~/.ssh
      - ssh-keyscan -H ************** >> ~/.ssh/known_hosts
      - chmod 644 ~/.ssh/known_hosts
  script:
    # 停止和删除目标服务器上的旧容器和镜像
    - ssh administrator@************** "docker ps -a --filter name=${SERVICE_NAME} -q | xargs -r docker stop | xargs -r docker rm"
    - ssh administrator@************** "docker rmi ${SERVICE_NAME}:latest || true"
    # 传输 tar 文件到目标服务器
    - scp ${TAR_FILE} administrator@**************:~/${TAR_FILE}
    # 加载 Docker 镜像并清理临时文件
    - ssh administrator@************** "docker load -i ${TAR_FILE}"
    - ssh administrator@**************   "rm ~/${TAR_FILE}"
    # 启动新的容器
    - ssh administrator@************** "docker run -d --name ${SERVICE_NAME} -p ${PORT_MAPPING} -e SECRET_KEY=${SECRET_KEY}  -v ~/mnt/DI:/mnt/DI  ${SERVICE_NAME}:latest"
  cache:
    # 在部署阶段也访问相同的缓存
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - ${TAR_FILE}
  retry: 2
  only:
    - main # 仅在 main 分支上执行
