package services

import (
	"errors"
	"log"
	"strconv"

	"web-api/internal/api/utils"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type User_Login_Service struct {
	*BaseService
}

var User_Login = &User_Login_Service{}

// Login authenticates a user by decrypting the AES-encrypted password sent from the client,
// comparing it against the stored password, and generating a JWT token on success.
func (s *User_Login_Service) Login(req *types.User_Login) (token string, err error) {
	// Panic recovery
	defer func() {
		if rec := recover(); rec != nil {
			err = errors.New("login failed due to unexpected error")
		}
	}()

	// Connect to DB
	db, errConn := database.TB_ERP_Connection()
	if errConn != nil {
		return "", errConn
	}
	sqlDB, errDB := db.DB()
	if errDB != nil {
		return "", errDB
	}
	defer sqlDB.Close()

	// Query user record
	const queryLogin = `
		SELECT GSBH, Busers.USERID, Busers.USERNAME, Busers.PWD
		FROM BLimits
		LEFT JOIN Busers ON Busers.USERID = BLimits.USERID
		WHERE Busers.USERID = ? AND GSBH = ?
		GROUP BY GSBH, Busers.USERID, Busers.USERNAME, Busers.PWD
	`
	var user types.User_Login
	if errScan := db.Raw(queryLogin, req.USERID, req.GSBH).Scan(&user).Error; errScan != nil {
		return "", errScan
	}

	// Check if account exists
	if user.USERID == "" {
		return "", errors.New("Account does not exist")
	}

	// Debug log: encrypted payload from client
	// log.Printf("Encrypted password received: %s", req.PWD)

	// Decrypt password
	plainPwd, errDec := utils.DecryptAES(req.PWD)
	if errDec != nil {
		log.Printf("Password decryption error: %v", errDec)
		return "", errors.New("Unable to decrypt password")
	}

	// Compare with stored password
	if user.PWD != plainPwd {
		return "", errors.New("Incorrect password")
	}

	// Convert USERID to int for JWT
	uid, errAtoi := strconv.Atoi(user.USERID)
	if errAtoi != nil {
		return "", errAtoi
	}

	// Generate JWT
	token, errGen := utils.GenerateJWT(uid, user.GSBH, user.USERNAME)
	if errGen != nil {
		return "", errGen
	}

	return token, nil
}
