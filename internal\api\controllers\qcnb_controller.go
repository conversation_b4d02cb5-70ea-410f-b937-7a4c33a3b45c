package controllers

import (
	"fmt"
	"log"
	"net/http"
	"sort"
	"strings"
	"time"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

type QIPController struct {
	*BaseController
}

var QIP = &QIPController{}

func (c *QIPController) ExportExcelHandler(ctx *gin.Context) {
	// 1) Bind params
	var req types.ExcelRes
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Thiếu hoặc sai tham số: " + err.Error()})
		return
	}

	// 2) Lấy data
	data, err := services.NBQC.XuatExcel(&req)
	if err != nil {
		log.Printf("Lỗi khi gọi XuatExcel: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Lỗi khi truy vấn dữ liệu"})
		return
	}

	// 3) Pivot thành map[date][LeanOrder]
	rowsByDate := make(map[string]map[int]types.ExcelTypes)
	var dates []string
	for _, rec := range data {
		day := rec.WorkDate.Format("2006-01-02")
		if rowsByDate[day] == nil {
			rowsByDate[day] = make(map[int]types.ExcelTypes)
			dates = append(dates, day)
		}
		rowsByDate[day][rec.LeanOrder] = rec
	}
	sort.Strings(dates)

	// 4) Tạo file và sheet
	f := excelize.NewFile()
	idx := f.NewSheet("Report")
	if err != nil {
		log.Printf("Lỗi khi tạo sheet Report: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Lỗi khi tạo sheet"})
		return
	}
	f.DeleteSheet(f.GetSheetName(0))
	f.SetActiveSheet(idx)
	const sheet = "Report"

	// 5) Styles
	topStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		Font:      &excelize.Font{Bold: true, Color: "#FFFFFF"},
		Fill:      excelize.Fill{Type: "pattern", Pattern: 1, Color: []string{"#17375E"}},
		Border:    allBorders(),
	})
	subStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		Font:      &excelize.Font{Bold: true, Color: "#000000"},
		Fill:      excelize.Fill{Type: "pattern", Pattern: 1, Color: []string{"#D9D9D9"}},
		Border:    allBorders(),
	})
	colHdrStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		Font:      &excelize.Font{Bold: true, Color: "#000000"},
		Fill:      excelize.Fill{Type: "pattern", Pattern: 1, Color: []string{"#F2F2F2"}},
		Border:    allBorders(),
	})
	dateStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Font:      &excelize.Font{Color: "#FFFFFF"},
		Fill:      excelize.Fill{Type: "pattern", Pattern: 1, Color: []string{"#17375E"}},
		Border:    allBorders(),
	})
	dataStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		Border:    allBorders(),
	})
	largeFontStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		Font:      &excelize.Font{Bold: true, Size: 16, Color: "#000000"},
		Fill:      excelize.Fill{Type: "pattern", Pattern: 1, Color: []string{"#D9D9D9"}},
		Border:    allBorders(),
	})
	defectTextStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		Border:    allBorders(),
	})
	leftAlignGreyStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
		Font:      &excelize.Font{Bold: true, Color: "#000000"},
		Fill:      excelize.Fill{Type: "pattern", Pattern: 1, Color: []string{"#D9D9D9"}},
		Border:    allBorders(),              // << giữ nguyên viền
	})

	// 6) Merge & vẽ header chính
	f.MergeCell(sheet, "A1", "B5")
	switch {

	case strings.HasPrefix(req.Line, "A7_CL%G"):
		f.SetCellValue(sheet, "A1", "Assembling\n成型")

	case strings.HasPrefix(req.Line, "A7_CL%M"):
		f.SetCellValue(sheet, "A1", "Stitching\n針車")

	case strings.HasPrefix(req.Line, "A7_CL%C"):
		f.SetCellValue(sheet, "A1", "Cutting\n次才段")

	case strings.HasPrefix(req.Line, "A7MLT%"):
		f.SetCellValue(sheet, "A1", "Computer Stitching\n电脑车")

	case strings.HasPrefix(req.Line, "A7_GCD%"):
		f.SetCellValue(sheet, "A1", "Stockfitting\n組底")

	default:
		f.SetCellValue(sheet, "A1", "Unknown Process\n未知工段")
	}

	f.SetCellStyle(sheet, "A1", "B5", topStyle)

	// 7) Merge & header phụ
	lines := []string{"Line 1", "Line 2", "Line 3", "Line 5"}
	leads := []string{"", "", "", ""}
	qcLeads := []string{"", "", "", ""}
	for i, line := range lines {
		start := 3 + i*4
		end := start + 3
		sc, ec := col(start), col(end)

		f.MergeCell(sheet, sc+"1", ec+"1")
		f.SetCellValue(sheet, sc+"1", line)
		f.SetCellStyle(sheet, sc+"1", ec+"1", largeFontStyle)

		f.MergeCell(sheet, sc+"2", ec+"2")
		f.SetCellValue(sheet, sc+"2", fmt.Sprintf("Line Lead 成型組長: %s", leads[i]))
		f.MergeCell(sheet, sc+"3", ec+"3")
		f.SetCellValue(sheet, sc+"3", "Supervisor 成型主管: 龚金国")
		f.MergeCell(sheet, sc+"4", ec+"4")
		f.SetCellValue(sheet, sc+"4", fmt.Sprintf("QC Lead 品管幹部: %s", qcLeads[i]))
		f.MergeCell(sheet, sc+"5", ec+"5")
		f.SetCellValue(sheet, sc+"5", "QC Supervisor 品管主管:")

		f.SetCellStyle(sheet, sc+"1", ec+"5", subStyle)
		f.SetCellStyle(sheet, sc+"2", sc+"2", leftAlignGreyStyle)
		f.SetCellStyle(sheet, sc+"3", sc+"3", leftAlignGreyStyle)
		f.SetCellStyle(sheet, sc+"4", sc+"4", leftAlignGreyStyle)
		f.SetCellStyle(sheet, sc+"5", sc+"5", leftAlignGreyStyle)
	}

	// 8) Header dòng 6
	f.SetCellValue(sheet, "A6", "Date\n日期")
	f.SetCellValue(sheet, "B6", "Target\n目標")
	f.SetCellStyle(sheet, "A6", "B6", topStyle)
	headers := []string{"Inspect\nQty\n檢驗雙數", "Defect\nQty\n不良雙數", "Defect Rate\n不良率", "Top Defects"}
	for i := range lines {
		for j, h := range headers {
			cell := fmt.Sprintf("%s6", col(3+i*4+j))
			f.SetCellValue(sheet, cell, h)
			f.SetCellStyle(sheet, cell, cell, colHdrStyle)
		}
	}

	// 9) Ghi dữ liệu + tính Week & Month Average
	orders := []int{1, 2, 3, 5}
	currentWeek := []string{}
	currentMonth := []string{}
	weekStart, monthStart := "", ""
	row := 7

	for i, d := range dates {
		t, _ := time.Parse("2006-01-02", d)
		weekday := t.Weekday()

		// Date & Target
		if weekday == time.Monday || (i == 0 && len(currentWeek) == 0) {
			weekStart = d
		}
		day := t.Day()
		if day == 1 || (i == 0 && len(currentMonth) == 0) {
			monthStart = d
		}

		currentWeek = append(currentWeek, d)
		currentMonth = append(currentMonth, d)

		f.SetCellValue(sheet, fmt.Sprintf("A%d", row), t.Format("1/2"))
		f.SetCellValue(sheet, fmt.Sprintf("B%d", row), "5%")
		f.SetCellStyle(sheet, fmt.Sprintf("A%d", row), fmt.Sprintf("B%d", row), dateStyle)

		// Fill data per line
		for idx := range lines {
			order := orders[idx]
			rec, ok := rowsByDate[d][order]
			if !ok {
				continue
			}
			base := 3 + idx*4

			// Tính Inspect Qty
			inspectQty := rec.Pro_Quantity + rec.Total_Defects

			// Cột Inspect Qty
			f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base), row), inspectQty)
			f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base), row), fmt.Sprintf("%s%d", col(base), row), dataStyle)

			// Cột Defect Qty
			f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base+1), row), rec.Total_Defects)
			f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base+1), row), fmt.Sprintf("%s%d", col(base+1), row), dataStyle)

			// Cột Defect Rate (Defect Qty / Inspect Qty)
			rate := 0.0
			if inspectQty > 0 {
				rate = float64(rec.Total_Defects) / float64(inspectQty)
			}
			f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base+2), row), fmt.Sprintf("%.2f%%", rate*100))
			f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base+2), row), fmt.Sprintf("%s%d", col(base+2), row), dataStyle)

			// Cột Top1 lý do (tiếng Anh)
			cell := fmt.Sprintf("%s%d", col(base+3), row)

			// Tách top lý do tiếng Anh
			yReasons := []string{}
			for _, p := range strings.Split(rec.YWSM, ",") {
				p = strings.TrimSpace(p)
				if p != "" {
					yReasons = append(yReasons, p)
				}
			}

			// Tách top lý do tiếng Trung
			zReasons := []string{}
			for _, p := range strings.Split(rec.ZWSM, ",") {
				p = strings.TrimSpace(p)
				if p != "" {
					zReasons = append(zReasons, p)
				}
			}

			// Ghi top 1 lý do tiếng Anh vào ô
			top1 := ""
			if len(yReasons) > 0 {
				top1 = yReasons[0]
			}
			f.SetCellValue(sheet, cell, top1)
			f.SetCellStyle(sheet, cell, cell, defectTextStyle)

			// Ghép comment gồm cả tiếng Anh + tiếng Trung
			commentLines := []string{}
			for i := 0; i < 3; i++ {
				if i < len(yReasons) {
					line := fmt.Sprintf("%d. %s", i+1, yReasons[i])
					if i < len(zReasons) {
						line += fmt.Sprintf(" - %s", zReasons[i])
					}
					commentLines = append(commentLines, line)
				}
			}

			commentText := strings.Join(commentLines, "\n")
			height := 8 + len(commentLines)*8
			if height > 40 {
				height = 40
			}

			escaped := strings.ReplaceAll(commentText, `"`, `\"`)
			escaped = strings.ReplaceAll(escaped, "\n", "\\n")

			commentJSON := fmt.Sprintf(`{"text":"%s","width":40,"height":%d,"visible":false}`, escaped, height)

			if err := f.AddComment(sheet, cell, commentJSON); err != nil {
				log.Printf("Lỗi khi thêm comment ở %s: %v", cell, err)
			}
		}

		row++

		if weekday == time.Saturday || i == len(dates)-1 {
			// Header
			f.SetCellValue(sheet, fmt.Sprintf("A%d", row), fmt.Sprintf("Week Average\n周平均 (%s - %s)", weekStart[5:], d[5:]))
			f.SetCellValue(sheet, fmt.Sprintf("B%d", row), "5%")
			f.SetCellStyle(sheet, fmt.Sprintf("A%d", row), fmt.Sprintf("B%d", row), topStyle)

			// Compute & fill
			for idx := range lines {
				order := orders[idx]
				proTotal := 0
				defTotal := 0

				// map[YWSM] = [count, ZWSM]
				type defInfo struct {
					count int
					zh    string
				}
				defMap := map[string]defInfo{}

				for _, wd := range currentWeek {
					if rec, ok := rowsByDate[wd][order]; ok {
						proTotal += rec.Pro_Quantity
						defTotal += rec.Total_Defects

						// Tách danh sách lỗi
						yList := strings.Split(rec.YWSM, ",")
						zList := strings.Split(rec.ZWSM, ",")

						for i := 0; i < len(yList); i++ {
							y := strings.TrimSpace(yList[i])
							if y == "" {
								continue
							}
							z := ""
							if i < len(zList) {
								z = strings.TrimSpace(zList[i])
							}
							info := defMap[y]
							info.count++
							if info.zh == "" {
								info.zh = z
							}
							defMap[y] = info
						}
					}
				}

				// Sắp xếp top lỗi
				type pair struct {
					yw string
					defInfo
				}
				defList := []pair{}
				for yw, info := range defMap {
					defList = append(defList, pair{yw, info})
				}
				sort.Slice(defList, func(i, j int) bool {
					return defList[i].count > defList[j].count
				})

				// Ghép comment
				top1 := ""
				commentLines := []string{}
				for i := 0; i < len(defList) && i < 3; i++ {
					if i == 0 {
						top1 = defList[i].yw
					}
					line := fmt.Sprintf("%d. %s - %s", i+1, defList[i].yw, defList[i].zh)
					commentLines = append(commentLines, line)
				}
				commentText := strings.Join(commentLines, "\n")
				escaped := strings.ReplaceAll(commentText, `"`, `\"`)
				escaped = strings.ReplaceAll(escaped, "\n", "\\n")
				height := 8 + len(commentLines)*8
				if height > 40 {
					height = 40
				}
				commentJSON := fmt.Sprintf(`{"text":"%s","width":40,"height":%d,"visible":false}`, escaped, height)

				// Ghi dữ liệu
				base := 3 + idx*4
				inspectQty := proTotal + defTotal

				// Inspect Qty
				f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base), row), inspectQty)
				f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base), row), fmt.Sprintf("%s%d", col(base), row), dataStyle)

				// Defect Qty
				f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base+1), row), defTotal)
				f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base+1), row), fmt.Sprintf("%s%d", col(base+1), row), dataStyle)

				// Defect Rate
				rate := 0.0
				if inspectQty > 0 {
					rate = float64(defTotal) / float64(inspectQty)
				}
				f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base+2), row), fmt.Sprintf("%.0f%%", rate*100))
				f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base+2), row), fmt.Sprintf("%s%d", col(base+2), row), dataStyle)

				// Top1 lỗi
				cell := fmt.Sprintf("%s%d", col(base+3), row)
				f.SetCellValue(sheet, cell, top1)
				f.SetCellStyle(sheet, cell, cell, defectTextStyle)

				// Ghi comment
				if err := f.AddComment(sheet, cell, commentJSON); err != nil {
					log.Printf("Lỗi khi thêm comment ở %s: %v", cell, err)
				}
			}
			row++
			currentWeek = []string{}
		}

		// Month Average at cuối tháng or last day
		nextDay := t.AddDate(0, 0, 1)
		if nextDay.Month() != t.Month() || i == len(dates)-1 {
			// Header
			f.SetCellValue(sheet, fmt.Sprintf("A%d", row), fmt.Sprintf("Month Average\n月平均 (%s)", monthStart[5:7]))
			f.SetCellValue(sheet, fmt.Sprintf("B%d", row), "5%")
			f.SetCellStyle(sheet, fmt.Sprintf("A%d", row), fmt.Sprintf("B%d", row), topStyle)

			// Compute & fill
			for idx := range lines {
				order := orders[idx]
				proTotal, defTotal := 0, 0

				// defMap[YWSM] = {count, ZWSM}
				type defInfo struct {
					count int
					zh    string
				}
				defMap := map[string]defInfo{}

				for _, md := range currentMonth {
					if rec, ok := rowsByDate[md][order]; ok {
						proTotal += rec.Pro_Quantity
						defTotal += rec.Total_Defects

						yList := strings.Split(rec.YWSM, ",")
						zList := strings.Split(rec.ZWSM, ",")

						for i := 0; i < len(yList); i++ {
							y := strings.TrimSpace(yList[i])
							if y == "" {
								continue
							}
							z := ""
							if i < len(zList) {
								z = strings.TrimSpace(zList[i])
							}
							info := defMap[y]
							info.count++
							if info.zh == "" {
								info.zh = z
							}
							defMap[y] = info
						}
					}
				}

				// Sắp xếp Top lỗi
				type pair struct {
					yw string
					defInfo
				}
				defList := []pair{}
				for yw, info := range defMap {
					defList = append(defList, pair{yw, info})
				}
				sort.Slice(defList, func(i, j int) bool {
					return defList[i].count > defList[j].count
				})

				// Ghi top1 lỗi
				top1 := ""
				commentLines := []string{}
				for i := 0; i < len(defList) && i < 3; i++ {
					if i == 0 {
						top1 = defList[i].yw
					}
					line := fmt.Sprintf("%d. %s - %s", i+1, defList[i].yw, defList[i].zh)
					commentLines = append(commentLines, line)
				}
				commentText := strings.Join(commentLines, "\n")
				escaped := strings.ReplaceAll(commentText, `"`, `\"`)
				escaped = strings.ReplaceAll(escaped, "\n", "\\n")
				height := 8 + len(commentLines)*8
				if height > 40 {
					height = 40
				}
				commentJSON := fmt.Sprintf(`{"text":"%s","width":40,"height":%d,"visible":false}`, escaped, height)

				// Ghi dữ liệu vào các ô
				base := 3 + idx*4
				inspectQty := proTotal + defTotal

				// Inspect Qty
				f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base), row), inspectQty)
				f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base), row), fmt.Sprintf("%s%d", col(base), row), dataStyle)

				// Defect Qty
				f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base+1), row), defTotal)
				f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base+1), row), fmt.Sprintf("%s%d", col(base+1), row), dataStyle)

				// Defect Rate
				rate := 0.0
				if inspectQty > 0 {
					rate = float64(defTotal) / float64(inspectQty)
				}
				f.SetCellValue(sheet, fmt.Sprintf("%s%d", col(base+2), row), fmt.Sprintf("%.0f%%", rate*100))
				f.SetCellStyle(sheet, fmt.Sprintf("%s%d", col(base+2), row), fmt.Sprintf("%s%d", col(base+2), row), dataStyle)

				// Top1 lỗi
				cell := fmt.Sprintf("%s%d", col(base+3), row)
				f.SetCellValue(sheet, cell, top1)
				f.SetCellStyle(sheet, cell, cell, defectTextStyle)

				if err := f.AddComment(sheet, cell, commentJSON); err != nil {
					log.Printf("Lỗi khi thêm comment ở %s: %v", cell, err)
				}
			}

			row++
			currentMonth = []string{}
		}

	}

	// 10) Đặt lại độ rộng cột
	colWidths := map[string]float64{
		"A": 14.71, "B": 7.14,
		"C": 11.29, "D": 9.57, "E": 13, "F": 20,
		"G": 11.29, "H": 9.57, "I": 13, "J": 20,
		"K": 11.29, "L": 9.57, "M": 13, "N": 20,
		"O": 11.29, "P": 9.57, "Q": 13, "R": 20,
	}
	for colName, w := range colWidths {
		f.SetColWidth(sheet, colName, colName, w)
	}

	// 11) Trả file
	filename := fmt.Sprintf("Assembling_Report_%s.xlsx", time.Now().Format("20060102_150405"))
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, filename))
	if err := f.Write(ctx.Writer); err != nil {
		log.Printf("Lỗi khi ghi Excel: %v", err)
	}
}

// Helper: full border style
func allBorders() []excelize.Border {
	return []excelize.Border{
		{Type: "left", Color: "#000000", Style: 1},
		{Type: "top", Color: "#000000", Style: 1},
		{Type: "right", Color: "#000000", Style: 1},
		{Type: "bottom", Color: "#000000", Style: 1},
	}
}

// helper: chuyển số cột thành tên (1→A, 27→AA)
func col(n int) string {
	name, _ := excelize.ColumnNumberToName(n)
	return name
}

func (c *QIPController) GetProductionDefectSummary(ctx *gin.Context) {
	var requestParams *types.ProductionDefectSummaryRequest
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.NBQC.GetProductionDefectSummary(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *QIPController) GetBDepartment(ctx *gin.Context) {
	result, err := services.NBQC.GetBDepartment()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *QIPController) GetLeanProcessDepartments(ctx *gin.Context) {
	var requestParams *types.BDepartment
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.NBQC.GetLeanProcessDepartments(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)

}

func (c *QIPController) XuatExcel(ctx *gin.Context) {
	var requestParams *types.ExcelRes
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.NBQC.XuatExcel(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)

}
