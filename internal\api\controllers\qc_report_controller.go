package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type QC_Report_Controller struct {
	*BaseController
}

var QC_Report = &QC_Report_Controller{}

func (c *QC_Report_Controller) GetTotalErrLeanInDay(ctx *gin.Context) {
	var requestParams *types.TotalErrleanindayRequest
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.QC_Report.GetTotalErrLeanInDay(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}
func (c *QC_Report_Controller) GetDPPMErrleaninday(ctx *gin.Context) {
	var requestParams *types.ProductionDefectSummaryRequest
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.QC_Report.GetDPPMErrleaninday(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)

}

func (c *QC_Report_Controller) GetDPPMErrAllLeanInDay(ctx *gin.Context) {
	var requestParams *types.TotalErrleanindayRequest
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.QC_Report.GetDPPMErrAllLeanInDay(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)

}

func (c *QC_Report_Controller) DailyDPPMByLeanReport(ctx *gin.Context) {
	var requestParams *types.TotalErrleanindayRequest
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.QC_Report.DailyDPPMByLeanReport(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)

}


func (c *QC_Report_Controller) GetDaily_Defect_DPPM_Report(ctx *gin.Context) {
	var requestParams *types.TotalErrleanindayRequest
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.QC_Report.GetDaily_Defect_DPPM_Report(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)

}
