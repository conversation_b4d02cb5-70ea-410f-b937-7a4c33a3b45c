package services

import (
	"log"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type QC_Report_Service struct {
	*BaseService
}

var QC_Report = &QC_Report_Service{}

func (s *QC_Report_Service) GetTotalErrLeanInDay(req *types.TotalErrleanindayRequest) ([]types.TotalErrleaninday, error) {
	var Errlean []types.TotalErrleaninday

	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	defer func() {
		dbInstance, _ := db.DB()
		dbInstance.Close()
	}()

	queryErrLean := `
			WITH Production AS (
				SELECT 
					bd.ID AS DepNo,
					bd.DepName,
					DATEADD(day, DATEDIFF(day, 0, sm.USERDATE), 0) AS ScanDateOnly,
					SUM(sm.CTS) AS TotalCTS
				FROM (
					SELECT DepNo, sum(SMZL.CTS*smddss.Qty) as CTS, SMZL.USERDATE
					FROM SMZL 
					left join smddss on smddss.codebar=SMZL.codebar  
					GROUP BY SMZL.DepNO , SMZL.USERDATE
					UNION ALL
					SELECT DepNo, CTS, ScanDate 
					FROM SMZLOld old
					WHERE NOT EXISTS (
						SELECT 1 FROM SMZL new
						WHERE new.DepNo = old.DepNo AND new.ScanDate = old.ScanDate
					)
				) sm
				LEFT JOIN BDepartment bd ON bd.ID = sm.DepNo
				WHERE sm.USERDATE >=  ?
					AND sm.USERDATE < ?
					AND bd.DepName LIKE ?
				GROUP BY bd.ID, bd.DepName, DATEADD(day, DATEDIFF(day, 0, sm.USERDATE), 0)
			),
			Defects AS (
				SELECT 
					qc.DepNo,
					bd.DepName,
					CAST(qc.UserDate AS DATE) AS ScanDateOnly,
					SUM(qcqd.Qty) AS Total_Defects
				FROM QCR qc
				LEFT JOIN QCRD qcqd ON qc.ProNo = qcqd.ProNo
				LEFT JOIN BDepartment bd ON bd.ID = qc.DepNo
				LEFT JOIN QCMAYY ON QCMAYY.YYBH = qcqd.YYBH
				WHERE qc.UserDate BETWEEN ? AND ?
				  AND bd.DepName LIKE ?
				  AND QCMAYY.GSBH = 'TBA'
				GROUP BY qc.DepNo, bd.DepName, CAST(qc.UserDate AS DATE)
			)
			SELECT 
				COALESCE(p.DepNo, d.DepNo) AS DepNo,
				COALESCE(p.DepName, d.DepName) AS DepName, 
				CASE 
					WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 ' 
					WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 ' 
					WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 ' 
					WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 '
					WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 ' 
					WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-01' THEN 'A7 LEAN1' 
					WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-02' THEN 'A7 LEAN2' 
					WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-03' THEN 'A7 LEAN3' 
					WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-04' THEN 'A7 LEAN4' 
					WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-05' THEN 'A7 LEAN5' 
					ELSE 'Unknown Lean'
				END 
				+ 
				CASE 
					WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'G' THEN 'ASSEMBLY'
					WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'C' THEN 'CUTTING'
					WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'M' THEN 'STITCHING' 
					WHEN p.DepName like '%GCD%' THEN ' STOCKFITTING' 
				END AS LeanName,
				CONVERT(VARCHAR(19), COALESCE(p.ScanDateOnly, d.ScanDateOnly), 120) AS ScanDate,
				(ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) AS Pro_Quantity,
				ISNULL(d.Total_Defects, 0) AS Total_Defects, 
				CASE 
					WHEN (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) > 0 
					THEN CAST(ISNULL(d.Total_Defects, 0) * 100.0 / (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) AS DECIMAL(5,2))
					ELSE 0
				END AS Defect_Rate_Percentage
			FROM Production p
			LEFT JOIN Defects d 
				ON p.DepNo = d.DepNo 
			AND p.ScanDateOnly = d.ScanDateOnly
			ORDER BY ScanDate, DepName; 
	`
	err = db.Raw(queryErrLean,
		req.StartDate,
		req.EndDate,
		req.DepName,
		req.StartDate,
		req.EndDate,
		req.DepName,
	).Scan(&Errlean).Error
	if err != nil {
		log.Printf("Query summary error: %v", err)
		return nil, err
	}

	return Errlean, nil
}

func (s *QC_Report_Service) GetDPPMErrleaninday(req *types.ProductionDefectSummaryRequest) ([]types.DPPMErrleaninday, error) {
	var DPPM []types.DPPMErrleaninday

	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	defer func() {
		dbInstance, _ := db.DB()
		dbInstance.Close()
	}()

	queryDPPMErrleaninday := `
		WITH Production AS (
			SELECT 
					bd.ID AS DepNo,
					bd.DepName,
					DATEADD(day, DATEDIFF(day, 0, sm.USERDATE), 0) AS ScanDateOnly,
					SUM(sm.CTS) AS TotalCTS
				FROM (
					SELECT DepNo, sum(SMZL.CTS*smddss.Qty) as CTS, SMZL.USERDATE
					FROM SMZL 
					left join smddss on smddss.codebar=SMZL.codebar  
					GROUP BY SMZL.DepNO , SMZL.USERDATE
					UNION ALL
					SELECT DepNo, CTS, ScanDate 
					FROM SMZLOld old
					WHERE NOT EXISTS (
						SELECT 1 FROM SMZL new
						WHERE new.DepNo = old.DepNo AND new.ScanDate = old.ScanDate
					)
				) sm
				LEFT JOIN BDepartment bd ON bd.ID = sm.DepNo
				WHERE sm.USERDATE >=  ?
					AND sm.USERDATE < ?
					AND bd.DepName LIKE ?
				GROUP BY bd.ID, bd.DepName, DATEADD(day, DATEDIFF(day, 0, sm.USERDATE), 0)
		),
		Defects AS (
				SELECT 
					qc.DepNo,
					bd.DepName,
					CAST(qc.UserDate AS DATE) AS ScanDateOnly,
					SUM(qcqd.Qty) AS Total_Defects
				FROM QCR qc
				LEFT JOIN QCRD qcqd ON qc.ProNo = qcqd.ProNo
				LEFT JOIN BDepartment bd ON bd.ID = qc.DepNo
				LEFT JOIN QCMAYY on QCMAYY.YYBH = qcqd.YYBH
				WHERE qc.UserDate BETWEEN ? and  ?
				  AND bd.DepName LIKE ?
				  AND QCMAYY.GSBH = 'TBA'
				GROUP BY qc.DepNo, bd.DepName, CAST(qc.UserDate AS DATE)
			)
		SELECT 
			COALESCE(p.DepNo, d.DepNo) AS DepNo,
			COALESCE(p.DepName, d.DepName) AS DepName, 

			-- Thêm cột LeanName
			CASE 
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 '			
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-01' THEN 'A7 LEAN1' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-02' THEN 'A7 LEAN2' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-03' THEN 'A7 LEAN3' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-04' THEN 'A7 LEAN4' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-05' THEN 'A7 LEAN5' 
				ELSE COALESCE(p.DepName, d.DepName)
			END 
			+  
			CASE 
				WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'G' THEN 'ASSEMBLY'
				WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'C' THEN 'CUTTING'
				WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'M' THEN 'STITCHING'
				WHEN p.DepName like '%GCD%' THEN ' STOCKFITTING'
				ELSE ''
			END AS LeanName,

			CONVERT(VARCHAR(19), COALESCE(p.ScanDateOnly, d.ScanDateOnly), 120) AS ScanDate,
			p.TotalCTS + ISNULL(d.Total_Defects, 0) AS Pro_Quantity,
			ISNULL(d.Total_Defects, 0) AS Total_Defects,
			CASE 
			WHEN (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) > 0 THEN 
				CASE 
					WHEN CAST(ISNULL(d.Total_Defects, 0) * 1000000.0 / (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) AS INT) = 1000000 
					THEN 0 
					ELSE CAST(ISNULL(d.Total_Defects, 0) * 1000000.0 / (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) AS INT)
				END
			ELSE 0
			END AS DPPM 
		FROM Production p
		LEFT JOIN Defects d 
			ON p.DepNo = d.DepNo 
		AND p.ScanDateOnly = d.ScanDateOnly
		ORDER BY ScanDate, DepName; 

	`
	err = db.Raw(queryDPPMErrleaninday,
		req.StartDate, req.EndDate, req.DepName, req.StartDate, req.EndDate, req.DepName).Scan(&DPPM).Error
	if err != nil {
		log.Printf("Query summary error: %v", err)
		return nil, err
	}

	return DPPM, nil
}

func (s *QC_Report_Service) GetDPPMErrAllLeanInDay(req *types.TotalErrleanindayRequest) ([]types.DPPMErrAllLeanInDay, error) {
	var DPPMAllLeanInDay []types.DPPMErrAllLeanInDay

	// Kết nối database
	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	// Truy vấn dữ liệu
	queryErrAllLeanInDay := `
WITH Production AS (
				SELECT 
					bd.ID AS DepNo,
					bd.DepName,
					SUM(sm.CTS) AS TotalCTS
				FROM (
					SELECT DepNo, sum(SMZL.CTS*smddss.Qty) as CTS, SMZL.USERDATE
					FROM SMZL 
					left join smddss on smddss.codebar=SMZL.codebar  
					GROUP BY SMZL.DepNO , SMZL.USERDATE
					UNION ALL
					SELECT DepNo, CTS, ScanDate 
					FROM SMZLOld old
					WHERE NOT EXISTS (
						SELECT 1 FROM SMZL new
						WHERE new.DepNo = old.DepNo AND new.ScanDate = old.ScanDate
					)
				) sm
				LEFT JOIN BDepartment bd ON bd.ID = sm.DepNo
				WHERE sm.USERDATE >=  ?
					AND sm.USERDATE < ?
					AND bd.DepName LIKE ?
				GROUP BY bd.ID, bd.DepName
),
Defects AS (
    SELECT 
        bd.ID AS DepNo,
        bd.DepName,
        SUM(qcqd.Qty) AS Total_Defects
    FROM QCR qc
    LEFT JOIN QCRD qcqd ON qc.ProNo = qcqd.ProNo
    LEFT JOIN BDepartment bd ON bd.ID = qc.DepNo
	LEFT JOIN QCMAYY MA ON MA.YYBH = qcqd.YYBH	
    WHERE qc.USERDATE BETWEEN  ? AND ?
        AND bd.DepName LIKE  ?
        AND bd.ID LIKE 'A%'
		AND MA.GSBH = 'TBA'
    GROUP BY bd.ID, bd.DepName
),
CombinedTimes AS (
    SELECT 
        bd.ID AS DepNo, 
        CONVERT(VARCHAR(16), sm.ScanDate, 120) AS ScanHour
    FROM 
    (
        SELECT DepNo, ScanDate FROM SMZL
        UNION
        SELECT DepNo, ScanDate FROM SMZLOld
    ) sm
    LEFT JOIN BDepartment bd ON bd.ID = sm.DepNo
    WHERE sm.ScanDate BETWEEN  ? AND ?
        AND bd.DepName LIKE ?
        AND bd.ID LIKE 'A%'
    
    UNION   
    SELECT 
        bd.ID AS DepNo, 
        CONVERT(VARCHAR(16), qc.USERDATE, 120) AS ScanHour
    FROM QCR qc
    LEFT JOIN BDepartment bd ON bd.ID = qc.DepNo
    WHERE qc.USERDATE  BETWEEN  ? AND  ?
        AND bd.DepName LIKE ?
        AND bd.ID LIKE 'A%'
),
TimeRange AS (
    SELECT 
        DepNo,
        MIN(ScanHour) AS StartTime,
        MAX(ScanHour) AS EndTime
    FROM CombinedTimes
    GROUP BY DepNo
)
SELECT 
    COALESCE(p.DepNo, d.DepNo) AS DepNo,
    COALESCE(p.DepName, d.DepName) AS DepName, 
	CASE 
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 '
				WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 '
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-01' THEN 'A7 LEAN1' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-02' THEN 'A7 LEAN2' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-03' THEN 'A7 LEAN3' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-04' THEN 'A7 LEAN4' 
				WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-05' THEN 'A7 LEAN5' 
				ELSE COALESCE(p.DepName, d.DepName)
			END 
			+  
			CASE 
				WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'G' THEN 'ASSEMBLY'
				WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'C' THEN 'CUTTING'
				WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'M' THEN 'STITCHING'
				WHEN p.DepName like '%GCD%' THEN ' STOCKFITTING'
				ELSE ''
			END AS LeanName,
    ISNULL(t.StartTime, 'N/A') + ' - ' + ISNULL(t.EndTime, 'N/A') AS TimeRange,
    (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) AS Pro_Quantity,
    ISNULL(d.Total_Defects, 0) AS Total_Defects,
    CASE 
    WHEN (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) > 0 THEN 
        CASE 
            WHEN CAST(ISNULL(d.Total_Defects, 0) * 1000000.0 / (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) AS INT) = 1000000 
            THEN 0 
            ELSE CAST(ISNULL(d.Total_Defects, 0) * 1000000.0 / (ISNULL(p.TotalCTS, 0) + ISNULL(d.Total_Defects, 0)) AS INT)
        END
    ELSE 0
	END AS DPPM 
FROM Production p
FULL OUTER JOIN Defects d 
    ON p.DepNo = d.DepNo
LEFT JOIN TimeRange t
    ON t.DepNo = COALESCE(p.DepNo, d.DepNo)
ORDER BY COALESCE(p.DepName, d.DepName); 
	`

	err = db.Raw(queryErrAllLeanInDay,
		req.StartDate, req.EndDate, req.DepName,
		req.StartDate, req.EndDate, req.DepName,
		req.StartDate, req.EndDate, req.DepName,
		req.StartDate, req.EndDate, req.DepName,
	).Scan(&DPPMAllLeanInDay).Error
	if err != nil {
		log.Printf("Query execution error: %v", err)
		return nil, err
	}

	return DPPMAllLeanInDay, nil
}

func (s *QC_Report_Service) DailyDPPMByLeanReport(req *types.TotalErrleanindayRequest) ([]types.DPPMErrleaninday, error) {
	var DailyDPPMByLeanReport []types.DPPMErrleaninday

	// Kết nối database
	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	// Truy vấn dữ liệu
	queryDailyDPPMByLeanReport := `
WITH Production AS (
   SELECT 
		bd.ID AS DepNo,
		bd.DepName,
		DATEADD(day, DATEDIFF(day, 0, sm.USERDATE), 0) AS ScanDateOnly,
		SUM(sm.CTS) AS TotalCTS
	FROM (
		SELECT DepNo, sum(SMZL.CTS*smddss.Qty) as CTS, SMZL.USERDATE
		FROM SMZL 
		left join smddss on smddss.codebar=SMZL.codebar  
		GROUP BY SMZL.DepNO , SMZL.USERDATE
		UNION ALL
		SELECT DepNo, CTS, ScanDate 
		FROM SMZLOld old
		WHERE NOT EXISTS (
			SELECT 1 FROM SMZL new
			WHERE new.DepNo = old.DepNo AND new.ScanDate = old.ScanDate
		)
	) sm
	LEFT JOIN BDepartment bd ON bd.ID = sm.DepNo
	WHERE sm.USERDATE >=  ?
		AND sm.USERDATE < ?
		AND bd.DepName LIKE ?
	GROUP BY bd.ID, bd.DepName, DATEADD(day, DATEDIFF(day, 0, sm.USERDATE), 0)
),
Defects AS (
				SELECT 
					qc.DepNo,
					bd.DepName,
					CAST(qc.UserDate AS DATE) AS ScanDateOnly,
					SUM(qcqd.Qty) AS Total_Defects
				FROM QCR qc
				LEFT JOIN QCRD qcqd ON qc.ProNo = qcqd.ProNo
				LEFT JOIN BDepartment bd ON bd.ID = qc.DepNo
				LEFT JOIN QCMAYY MA ON MA.YYBH = qcqd.YYBH				
				WHERE qc.UserDate BETWEEN ? AND ?
				  AND bd.DepName LIKE ?
				  AND MA.GSBH = 'TBA'
				GROUP BY qc.DepNo, bd.DepName, CAST(qc.UserDate AS DATE)
			)
SELECT 
    COALESCE(p.DepNo, d.DepNo) AS DepNo,
    COALESCE(p.DepName, d.DepName) AS DepName, 
	
    (
        CASE 
            WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 '
            WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 '
            WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 '
            WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 '
            WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 '
			WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-01' THEN 'A7 LEAN1' 
			WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-02' THEN 'A7 LEAN2' 
			WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-03' THEN 'A7 LEAN3' 
			WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-04' THEN 'A7 LEAN4' 
			WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-05' THEN 'A7 LEAN5' 
            ELSE COALESCE(p.DepName, d.DepName)
        END
        +
        CASE 
            WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'G' THEN 'ASSEMBLY'
            WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'C' THEN 'CUTTING'
            WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'M' THEN 'STITCHING'
			WHEN p.DepName like '%GCD%' THEN ' STOCKFITTING'
            ELSE ''
        END
    ) AS LeanName,
	CASE 
        WHEN (p.TotalCTS + ISNULL(d.Total_Defects, 0)) > 0 
        THEN CAST(ISNULL(d.Total_Defects, 0) * 1000000.0 / (p.TotalCTS + ISNULL(d.Total_Defects, 0)) AS INT)
        ELSE 0
    END AS DPPM,
    CONVERT(VARCHAR(19), COALESCE(p.ScanDateOnly, d.ScanDateOnly), 120) AS ScanDate,
    p.TotalCTS + ISNULL(d.Total_Defects, 0) AS Pro_Quantity,
    ISNULL(d.Total_Defects, 0) AS Total_Defects
    
FROM Production p
LEFT JOIN Defects d 
    ON p.DepNo = d.DepNo 
   AND p.ScanDateOnly = d.ScanDateOnly
ORDER BY ScanDate, DepName;

	`

	err = db.Raw(queryDailyDPPMByLeanReport,
		req.StartDate, req.EndDate, req.DepName,
		req.StartDate, req.EndDate, req.DepName).Scan(&DailyDPPMByLeanReport).Error
	if err != nil {
		log.Printf("Query execution error: %v", err)
		return nil, err
	}

	return DailyDPPMByLeanReport, nil
}

func (s *QC_Report_Service) GetDaily_Defect_DPPM_Report(req *types.TotalErrleanindayRequest) ([]types.Daily_Defect_DPPM_Report, error) {
	var DailyDPPMReport []types.Daily_Defect_DPPM_Report

	// Kết nối database
	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	// Truy vấn dữ liệu
	queryDPPMReport := `
WITH DefectsDetails AS ( 
	SELECT 
		QCR.DepNo,
		BD.DepName,  
		QCMAYYSS.ID AS MajorErrorID,
		QCMAYYSS.YWSM ,
		QCMAYYSS.ZWSM ,
		SUM(QCRD.Qty) AS Total_Defects
	FROM QCR
	LEFT JOIN QCRD ON QCR.ProNo = QCRD.ProNo
	LEFT JOIN BDepartment BD ON BD.ID = QCR.DepNO
	LEFT JOIN QCMAYY ON QCMAYY.YYBH = QCRD.YYBH
	LEFT JOIN QCMAYYSS ON QCMAYYSS.ID = QCMAYY.MajorErrorID
	WHERE BD.DepName = ?
	  AND QCMAYY.GSBH = 'TBA'
	  AND QCR.USERDATE BETWEEN ? AND ? 
	GROUP BY QCR.DepNo, BD.DepName, QCMAYYSS.ID, 
	QCMAYYSS.YWSM,
	QCMAYYSS.ZWSM 
),

Production AS (
	SELECT 
		S.DepNo,
		BD.DepName,
		SUM(S.CTS) AS TotalCTS
	FROM (
		SELECT DepNo, CTS, USERDATE
		FROM SMZLOld
		WHERE USERDATE BETWEEN ? AND ?
			AND NOT EXISTS (
				SELECT 1 
				FROM SMZL
				WHERE SMZL.DepNo = SMZLOld.DepNo
				  AND SMZLOld.USERDATE = SMZL.USERDATE
				  AND SMZLOld.CTS = SMZL.CTS
			)
		
		UNION ALL
		
		SELECT SMZL.DepNo, SUM(SMZL.CTS * smddss.Qty) AS CTS, SMZL.USERDATE
		FROM SMZL
		LEFT JOIN smddss ON smddss.codebar = SMZL.codebar
		WHERE SMZL.USERDATE BETWEEN ? AND ?
		GROUP BY SMZL.DepNo, SMZL.USERDATE
	) S
	JOIN BDepartment BD ON BD.ID = S.DepNo
	WHERE BD.ID LIKE 'A%' 
	  AND BD.DepName = ? --'A7_CL1-1_G'
	GROUP BY S.DepNo, BD.DepName
)

SELECT 
	d.DepNo,
	d.DepName,
	CASE 
		WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 '
		WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 '
		WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 '
		WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 '
		WHEN COALESCE(p.DepName, d.DepName) LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 '
		WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-01' THEN 'A7 LEAN1' 
		WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-02' THEN 'A7 LEAN2' 
		WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-03' THEN 'A7 LEAN3' 
		WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-04' THEN 'A7 LEAN4' 
		WHEN COALESCE(p.DepName, d.DepName) = 'A7_GCD-05' THEN 'A7 LEAN5' 
		ELSE COALESCE(p.DepName, d.DepName)
	END 
	+
	CASE 
		WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'G' THEN 'ASSEMBLY'
		WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'C' THEN 'CUTTING'
		WHEN RIGHT(COALESCE(p.DepName, d.DepName), 1) = 'M' THEN 'STITCHING'
		WHEN p.DepName like '%GCD%' THEN ' STOCKFITTING'
		ELSE ''
	END AS LeanName,

	d.YWSM,
	d.ZWSM,
	d.Total_Defects,
	ISNULL(p.TotalCTS, 0) AS TotalCTS,

	CASE 
		WHEN ISNULL(p.TotalCTS, 0) > 0 THEN 
			CAST(d.Total_Defects * 1000000.0 / p.TotalCTS AS INT)
		ELSE 0
	END AS DPPM

FROM DefectsDetails d
LEFT JOIN Production p ON d.DepNo = p.DepNo
ORDER BY d.Total_Defects DESC;

	`

	err = db.Raw(queryDPPMReport, req.DepName,
		req.StartDate, req.EndDate,
		req.StartDate, req.EndDate,
		req.StartDate, req.EndDate,
		req.DepName,
	).Scan(&DailyDPPMReport).Error
	if err != nil {
		log.Printf("Query execution error: %v", err)
		return nil, err
	}

	return DailyDPPMReport, nil
}
