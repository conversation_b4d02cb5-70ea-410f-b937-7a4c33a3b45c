package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type Defect_Qty_By_Line_Controller struct {
	*BaseController
}

var Defect_Qty_By_Line = &Defect_Qty_By_Line_Controller{}

func (c *Defect_Qty_By_Line_Controller) GetHourlyDefectsByLean(ctx *gin.Context) {
	var requestParams *types.HourlyDefects
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.Defect_Qty_By_Line.GetDefectsQtyByLine(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)

}
