package types

import "time"

type ExcelTypes struct {
	WorkDate      time.Time `json:"WorkDate"`
	DepNo         string    `json:"DepNo"`
	LeanName      string    `json:"LeanName"`
	TimeRange     string    `json:"TimeRange"`
	Pro_Quantity  int       `json:"Pro_Quantity"`
	Total_Defects int       `json:"Total_Defects"`
	Defect_Rate   float64   `json:"Defect_Rate"`
	YWSM          string    `json:"YWSM"`
	ZWSM          string    `json:"ZWSM"` 
	LeanOrder     int       `json:"LeanOrder"`
}
type ExcelRes struct {
	StartDate string `json:"StartDate"`
	EndDate   string `json:"EndDate"`
	Line      string `json:"Line"`
}
