package utils

import (
    "log"
    "os"

    "github.com/joho/godotenv"
)

// SecretKey là chuỗi bí mật dùng cho AES và JWT
var SecretKey string

func init() {
    if err := godotenv.Load(); err != nil {
        log.Println("Không tìm thấy file .env, sẽ dùng biến môi trường hệ thống")
    }

    SecretKey = os.Getenv("SECRET_KEY")
    if SecretKey == "" {
        log.Fatal("ERROR: biến môi trường SECRET_KEY chưa được thiết lập")
    }
}
