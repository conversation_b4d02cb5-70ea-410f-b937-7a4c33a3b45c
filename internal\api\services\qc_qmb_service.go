package services

import (
	"fmt"
	"log"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type NBQCService struct {
	*BaseService
}

var NBQC = &NBQCService{}

func (s *NBQCService) GetProductionDefectSummary(req *types.ProductionDefectSummaryRequest) ([]types.ProductionDefectSummary, error) {
	var summaries []types.ProductionDefectSummary
	var analyses []types.DefectAnalysis

	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	defer func() {
		dbInstance, _ := db.DB()
		dbInstance.Close()
	}()

	querySummary := `
			WITH Production AS (
				SELECT 
					C.DepNo,
					BD.DepName,
					(C.CTS + ISNULL(C.Qty, 0)) AS Pro_Quantity,
					BD.GXLB  
				FROM (
					SELECT 
						A.DepNo, 
						A.CT<PERSON>,
						B.Qty
					FROM ( 
						SELECT DepNo, SUM(CTS) AS CTS
						FROM ( 
							SELECT DepNo, CTS, USERDATE
							FROM SMZLOld
							WHERE USERDATE BETWEEN ? AND ?
							AND NOT EXISTS (
								SELECT 1 
								FROM SMZL 
								WHERE SMZL.DepNo = SMZLOld.DepNo 
									AND SMZLOld.USERDATE = SMZL.USERDATE 
									AND SMZLOld.CTS = SMZL.CTS
							)
							UNION ALL 
							SELECT DepNo, sum(SMZL.CTS*smddss.Qty) as CTS, SMZL.USERDATE
							FROM SMZL
							left join smddss on smddss.codebar=SMZL.codebar  
							WHERE SMZL.USERDATE BETWEEN ? AND ?
							GROUP BY SMZL.DepNO , SMZL.USERDATE
						) AS CombinedData
						GROUP BY DepNo
					) A
					LEFT JOIN ( 
						SELECT DepNo, SUM(Qty) AS Qty
						FROM QCR
						LEFT JOIN QCRD ON QCR.ProNo = QCRD.ProNo
						WHERE QCRD.USERDATE BETWEEN ? AND ?
						GROUP BY DepNo
					) B ON A.DepNo = B.DepNo
				) C
				LEFT JOIN BDepartment BD ON BD.ID = C.DepNo
				WHERE BD.DepName LIKE ?
			),
			Defects AS ( 
				SELECT  
					QCR.DepNo,
					SUM(QCRD.Qty) AS TotalDefect
				FROM QCR
				LEFT JOIN QCRD ON QCR.ProNo = QCRD.ProNo
				LEFT JOIN BDepartment BD ON BD.ID = QCR.DepNO
				LEFT JOIN QCMAYY ON QCMAYY.YYBH = QCRD.YYBH
				WHERE BD.DepName LIKE ?
				AND QCR.USERDATE BETWEEN ? AND ?
				AND QCMAYY.GSBH = 'TBA'
				GROUP BY QCR.DepNo
			),
			TimeRange AS ( 
				SELECT 
					DepNo,
					CONVERT(VARCHAR(16), MIN(USERDATE), 120) AS StartTime,
					CONVERT(VARCHAR(16), MAX(USERDATE), 120) AS EndTime
				FROM (
					SELECT DepNo, USERDATE
					FROM SMZLOld
					WHERE USERDATE BETWEEN ? AND ?
					AND DepNo IN (SELECT ID FROM BDepartment WHERE DepName LIKE ?)
					UNION ALL
					SELECT DepNo, USERDATE
					FROM SMZL
					WHERE USERDATE BETWEEN ? AND ?
					AND DepNo IN (SELECT ID FROM BDepartment WHERE DepName LIKE ?)
				) T
				GROUP BY DepNo
			)
			SELECT 
				P.DepNo,
				-- Thay đổi cách ánh xạ LeanName
				CASE 
					WHEN P.DepName LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 ' 
					WHEN P.DepName LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 ' 
					WHEN P.DepName LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 ' 
					WHEN P.DepName LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 ' 
					WHEN P.DepName LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 ' 
					ELSE P.DepName
				END 
				+ 
				CASE 
					WHEN P.GXLB = 'A' THEN ' ASSEMBLY'
					WHEN P.GXLB = 'C' THEN ' CUTTING'
					WHEN P.GXLB = 'S' THEN ' STITCHING'
					ELSE ''
				END AS LeanName,

				t.StartTime + ' - ' + t.EndTime AS TimeRange,
				P.Pro_Quantity,
				D.TotalDefect AS Total_Defects,
				CAST(D.TotalDefect * 100.0 / NULLIF(P.Pro_Quantity, 0) AS DECIMAL(10,2)) AS Defect_Rate
			FROM Production P
			LEFT JOIN Defects D ON P.DepNo = D.DepNo
			LEFT JOIN TimeRange t ON P.DepNo = t.DepNo

	`

	err = db.Raw(
		querySummary,
		req.StartDate, req.EndDate,
		req.StartDate, req.EndDate,
		req.StartDate, req.EndDate,
		req.DepName,
		req.DepName, req.StartDate, req.EndDate,
		req.StartDate, req.EndDate, req.DepName,
		req.StartDate, req.EndDate, req.DepName,
	).Scan(&summaries).Error
	if err != nil {
		log.Printf("Query summary error: %v", err)
		return nil, err
	}

	queryAnalysis := `
		
			WITH DefectsDetails AS ( 
			SELECT 
				QCR.DepNo,
				BD.DepName,
				QCMAYYSS.YWSM ,
				 QCMAYYSS.ZWSM ,
				SUM(QCRD.Qty) AS Total_Defects
			FROM QCR
			LEFT JOIN QCRD ON QCR.ProNo = QCRD.ProNo
			LEFT JOIN BDepartment BD ON BD.ID = QCR.DepNO		
			LEFT JOIN QCMAYY ON QCMAYY.YYBH = QCRD.YYBH
			LEFT JOIN QCMAYYSS on QCMAYYSS.ID = QCMAYY.MajorErrorID
			WHERE BD.DepName = ?
			  AND QCR.USERDATE BETWEEN ? AND ?
			  AND QCMAYY.GSBH = 'TBA'
			GROUP BY QCR.DepNo, BD.DepName, 
					 QCMAYYSS.YWSM, 
					 QCMAYYSS.ZWSM
		),
		TotalDayDefects AS ( 
			SELECT DepNo, SUM(Total_Defects) AS DayTotalDefect
			FROM DefectsDetails
			GROUP BY DepNo
		),
		DefectsWithPercent AS ( 
			SELECT 
				D.DepNo,
				D.YWSM,
				D.ZWSM,
				D.Total_Defects,
				TD.DayTotalDefect,
				CAST(D.Total_Defects * 100.0 / TD.DayTotalDefect AS DECIMAL(10,2)) AS Defect_Percentage
			FROM DefectsDetails D
			LEFT JOIN TotalDayDefects TD ON D.DepNo = TD.DepNo
		),
		RankedDefects AS ( 
			SELECT 
				DepNo,
				YWSM,
				ZWSM,
				Total_Defects,
				Defect_Percentage,
				ROW_NUMBER() OVER (PARTITION BY DepNo ORDER BY Total_Defects DESC) AS Item
			FROM DefectsWithPercent
		)
		-- Phần 1: Lấy top 4 lỗi với cumulative tính tích lũy từ trên xuống theo thứ tự xếp hạng
		SELECT 
			r.DepNo,
			bd.DepName, 
			r.YWSM,
			r.ZWSM,
			r.Total_Defects,
			r.Defect_Percentage,
			CASE 
				WHEN (
					SELECT SUM(r2.Defect_Percentage)
					FROM RankedDefects r2
					WHERE r2.DepNo = r.DepNo
					AND r2.Item <= 4
					AND r2.Item <= r.Item
				) > 99.90 THEN 100.00
				ELSE (
					SELECT SUM(r2.Defect_Percentage)
					FROM RankedDefects r2
					WHERE r2.DepNo = r.DepNo
					AND r2.Item <= 4
					AND r2.Item <= r.Item
				)
			END AS Cumulative_Percentage,
			r.Item
		FROM RankedDefects r
		LEFT JOIN BDepartment bd ON bd.ID = r.DepNo
		WHERE r.Item <= 4

		UNION ALL
		
		SELECT 
			dp.DepNo,
			bd.DepName, 
			'Other' AS YWSM,
			'Khac' AS ZWSM,
			SUM(dp.Total_Defects) AS Total_Defects,
			CAST(SUM(dp.Total_Defects) * 100.0 / MAX(dp.DayTotalDefect) AS DECIMAL(10,2)) AS Defect_Percentage,
			100 AS Cumulative_Percentage, -- Ép cột này luôn hiển thị 100%
			5 AS Item
		FROM (
			SELECT 
				d.DepNo,
				d.YWSM,
				d.ZWSM,
				d.Total_Defects,
				d.Defect_Percentage,
				td.DayTotalDefect,
				ROW_NUMBER() OVER (PARTITION BY d.DepNo ORDER BY d.Total_Defects DESC) AS Item
			FROM DefectsWithPercent d
			LEFT JOIN TotalDayDefects td ON d.DepNo = td.DepNo
		) dp
		LEFT JOIN BDepartment bd ON bd.ID = dp.DepNo
		WHERE dp.Item > 4
		GROUP BY dp.DepNo, bd.DepName
		ORDER BY Item;

	`

	err = db.Raw(queryAnalysis, req.DepName, req.StartDate, req.EndDate).Scan(&analyses).Error
	if err != nil {
		log.Printf("Query analysis error: %v", err)
		return nil, err
	}

	for i := range summaries {
		for _, a := range analyses {
			if summaries[i].DepNo == a.DepNo {
				summaries[i].Data = append(summaries[i].Data, a)
			}
		}
		if summaries[i].Data == nil {
			summaries[i].Data = []types.DefectAnalysis{}
		}
	}

	return summaries, nil
}

func (s *NBQCService) GetBDepartment() ([]types.BDepartment, error) {
	var BDep []types.BDepartment

	// Kết nối DB
	db, err := database.TB_ERP_Connection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	query := `
	SELECT 
		DepName,
		CASE 
			WHEN DepName = 'A7_CL1' THEN 'A7_LEAN1'
			WHEN DepName = 'A7_CL2' THEN 'A7_LEAN2'
			ELSE DepName
		END AS LeanName
	FROM BDepartment
	WHERE DepName LIKE 'A7_CL%' AND DepName NOT LIKE '%-%'
	`

	// Thực hiện truy vấn
	err = db.Raw(query).Scan(&BDep).Error
	if err != nil {
		fmt.Println("Query execution error:", err)
		return nil, err
	}

	return BDep, nil
}

func (s *NBQCService) GetLeanProcessDepartments(req *types.BDepartment) ([]types.BDepartment, error) {
	var LeanProcessDepartments []types.BDepartment
	// Kết nối database
	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	// Truy vấn dữ liệu
	query := ` 
			SELECT 
				DepName,
				CASE 
					WHEN DepName LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 ' + ProcessName
					WHEN DepName LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 ' + ProcessName
					WHEN DepName LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 ' + ProcessName
					WHEN DepName LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 ' + ProcessName
					WHEN DepName LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 ' + ProcessName
					ELSE DepName
				END AS LeanName
			FROM BDepartment
			JOIN (VALUES 
				('A', 'ASSEMBLY'),
				('C', 'CUTTING'),
				('S', 'STITCHING')
			) AS ProcessMap (GXLB, ProcessName)
			ON BDepartment.GXLB = ProcessMap.GXLB
			WHERE DepName LIKE ?
			ORDER BY
				CASE 
					WHEN 
						CHARINDEX('-', DepName) > 0 
						AND CHARINDEX('_', DepName, CHARINDEX('-', DepName)) > 0
						AND ISNUMERIC(SUBSTRING(
								DepName, 
								CHARINDEX('-', DepName) + 1, 
								CHARINDEX('_', DepName, CHARINDEX('-', DepName)) - CHARINDEX('-', DepName) - 1
							)) = 1 
					THEN CAST(SUBSTRING(
								DepName, 
								CHARINDEX('-', DepName) + 1, 
								CHARINDEX('_', DepName, CHARINDEX('-', DepName)) - CHARINDEX('-', DepName) - 1
							) AS INT)
					ELSE 0 
				END, 
				CASE 
					WHEN RIGHT(DepName, 1) = 'C' THEN 1
					WHEN RIGHT(DepName, 1) = 'M' THEN 2
					WHEN RIGHT(DepName, 1) = 'G' THEN 3
					ELSE 4
				END
	`
	//DepName LIKE ? --tham số vào đây vd: '%A7_CL1%' hoặc '%A7_CL2%'
	//BDepartment.GXLB = ? -- tham số vào đây vd: 'A' hoặc 'C' hoặc 'S'
	err = db.Raw(query, req.DepName).Scan(&LeanProcessDepartments).Error
	if err != nil {
		log.Printf("Query execution error: %v", err)
		return nil, err
	}
	return LeanProcessDepartments, nil
}

func (s *NBQCService) XuatExcel(req *types.ExcelRes) ([]types.ExcelTypes, error) {
	var Ex []types.ExcelTypes
	// Kết nối database
	db, err := database.TB_ERP_Connection()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	if err := db.Exec("SET ARITHABORT ON").Error; err != nil {
		log.Printf("Failed to set ARITHABORT ON: %v", err)
		return nil, err
	}

	// Truy vấn dữ liệu
	query := `
	SET ARITHABORT ON;
			DECLARE 
			@StartDate DATETIME = ?,
			@EndDate   DATETIME = ?,
			@Line      NVARCHAR(100) = ? ;
 
    WITH Production AS (
    SELECT 
        CAST(S.USERDATE AS DATE)    AS WorkDate,
        S.DepNo,
        BD.DepName,
        BD.GXLB,
        SUM(S.CTS)                  AS Pro_Quantity
    FROM (
        SELECT DepNo, CTS, USERDATE
        FROM SMZLOld
        WHERE USERDATE BETWEEN @StartDate AND @EndDate
          AND NOT EXISTS (
            SELECT 1 
            FROM SMZL
            WHERE SMZL.DepNo = SMZLOld.DepNo
              AND SMZLOld.USERDATE = SMZL.USERDATE
              AND SMZLOld.CTS = SMZL.CTS
          )
        UNION ALL
        SELECT DepNo, sum(SMZL.CTS*smddss.Qty) as CTS, SMZL.USERDATE
        FROM SMZL
		left join smddss on smddss.codebar=SMZL.codebar
        WHERE SMZL.USERDATE BETWEEN @StartDate AND @EndDate
		GROUP BY SMZL.DepNO , SMZL.USERDATE
    ) S
    JOIN BDepartment BD 
      ON BD.ID = S.DepNo
    WHERE BD.DepName LIKE @Line
    GROUP BY 
        CAST(S.USERDATE AS DATE), 
        S.DepNo, 
        BD.DepName, 
        BD.GXLB
),

Defects AS (
    SELECT
        CAST(Q.USERDATE AS DATE)    AS WorkDate,
        Q.DepNo,
        SUM(D.Qty)                  AS TotalDefect
    FROM QCR Q
    JOIN QCRD D       ON Q.ProNo = D.ProNo
    JOIN BDepartment BD ON BD.ID = Q.DepNo
	LEFT JOIN QCMAYY MAY ON MAY.YYBH = D.YYBH
    WHERE BD.DepName LIKE @Line
      AND Q.USERDATE BETWEEN @StartDate AND @EndDate
	  and MAY.GSBH = 'TBA'
    GROUP BY 
        CAST(Q.USERDATE AS DATE), 
        Q.DepNo

),

TimeRange AS (
    SELECT
        CAST(T.USERDATE AS DATE)    AS WorkDate,
        T.DepNo,
        CONVERT(VARCHAR(16), MIN(T.USERDATE), 120) AS StartTime,
        CONVERT(VARCHAR(16), MAX(T.USERDATE), 120) AS EndTime
    FROM (
        SELECT DepNo, USERDATE FROM SMZLOld
        WHERE USERDATE BETWEEN @StartDate AND @EndDate
          AND DepNo IN (SELECT ID FROM BDepartment WHERE DepName LIKE @Line)
        UNION ALL
        SELECT DepNo, USERDATE FROM SMZL
        WHERE USERDATE BETWEEN @StartDate AND @EndDate
          AND DepNo IN (SELECT ID FROM BDepartment WHERE DepName LIKE @Line)
    ) T
    GROUP BY 
        CAST(T.USERDATE AS DATE),
        T.DepNo
),

DefectsDetails AS (
    SELECT 
        CAST(Q.USERDATE AS DATE)    AS WorkDate,
        Q.DepNo,
        BD.DepName,
        MSS.YWSM,
        MSS.ZWSM,
        SUM(D.Qty)                  AS Total_Defects
    FROM QCR Q
    JOIN QCRD D         ON Q.ProNo = D.ProNo
    JOIN BDepartment BD ON BD.ID = Q.DepNo
    LEFT JOIN QCMAYY BM  ON BM.YYBH  = D.YYBH
    LEFT JOIN QCMAYYSS MSS ON MSS.ID = BM.MajorErrorID
    WHERE BD.DepName LIKE @Line
      AND Q.USERDATE BETWEEN @StartDate AND @EndDate
	  AND BM.GSBH = 'TBA'
    GROUP BY 
        CAST(Q.USERDATE AS DATE),
        Q.DepNo, 
        BD.DepName, 
        MSS.YWSM,
        MSS.ZWSM
),
 
TopDefectsAgg AS (
    SELECT 
        WorkDate,
        DepNo, 
        STUFF(( 
            SELECT TOP 3 ', ' + YWSM
            FROM (
                SELECT 
                    WorkDate,
                    DepNo,
                    YWSM,
                    Total_Defects,
                    ROW_NUMBER() OVER (PARTITION BY WorkDate, DepNo ORDER BY Total_Defects DESC) AS RN
                FROM DefectsDetails
            ) T2
            WHERE T2.WorkDate = T1.WorkDate AND T2.DepNo = T1.DepNo AND RN <= 3
            ORDER BY Total_Defects DESC
            FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS YWSM_Top3,
			 
        STUFF((
            SELECT TOP 3 ', ' + ZWSM
            FROM (
                SELECT 
                    WorkDate,
                    DepNo,
                    ZWSM,
                    Total_Defects,
                    ROW_NUMBER() OVER (PARTITION BY WorkDate, DepNo ORDER BY Total_Defects DESC) AS RN
                FROM DefectsDetails
            ) T3
            WHERE T3.WorkDate = T1.WorkDate AND T3.DepNo = T1.DepNo AND RN <= 3
            ORDER BY Total_Defects DESC
            FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS ZWSM_Top3
    FROM (
        SELECT DISTINCT WorkDate, DepNo FROM DefectsDetails
    ) T1
)
	SELECT 
    P.WorkDate,
    P.DepNo,
    CASE 
        WHEN P.DepName LIKE 'A7_CL1-1_%' THEN 'A7 LEAN1 ' 
        WHEN P.DepName LIKE 'A7_CL1-2_%' THEN 'A7 LEAN2 ' 
        WHEN P.DepName LIKE 'A7_CL1-3_%' THEN 'A7 LEAN3 ' 
        WHEN P.DepName LIKE 'A7_CL1-4_%' THEN 'A7 LEAN4 ' 
        WHEN P.DepName LIKE 'A7_CL2-5_%' THEN 'A7 LEAN5 '
		WHEN P.DepName = 'A7_GCD-01' THEN 'A7 LEAN1 '
		WHEN P.DepName = 'A7_GCD-02' THEN 'A7 LEAN2 '
		WHEN P.DepName = 'A7_GCD-03' THEN 'A7 LEAN3 '
		WHEN P.DepName = 'A7_GCD-04' THEN 'A7 LEAN4 '
		WHEN P.DepName = 'A7_GCD-05' THEN 'A7 LEAN5 '
        ELSE P.DepName
    END
    +
    CASE 
        WHEN P.GXLB = 'A' THEN ' ASSEMBLY'
        WHEN P.GXLB = 'C' THEN ' CUTTING'
        WHEN P.GXLB = 'S' THEN ' STITCHING'
		WHEN P.GXLB = 'O' THEN ' STOCKFITTING'
        ELSE ''
    END AS LeanName,
    T.StartTime + ' - ' + T.EndTime   AS TimeRange,
    P.Pro_Quantity,
    ISNULL(D.TotalDefect, 0)         AS Total_Defects,
    CAST(ISNULL(D.TotalDefect, 0) * 100.0 
         / NULLIF(P.Pro_Quantity, 0) AS DECIMAL(10,2)) AS Defect_Rate,
    TD.YWSM_Top3                      AS YWSM,
    TD.ZWSM_Top3                      AS ZWSM, 
    CASE 
        WHEN P.DepName LIKE 'A7_CL1-1_%' THEN 1
        WHEN P.DepName LIKE 'A7_CL1-2_%' THEN 2
        WHEN P.DepName LIKE 'A7_CL1-3_%' THEN 3
        WHEN P.DepName LIKE 'A7_CL1-4_%' THEN 4
        WHEN P.DepName LIKE 'A7_CL2-5_%' THEN 5
		WHEN P.DepName = 'A7_GCD-01' THEN 1
		WHEN P.DepName = 'A7_GCD-02' THEN 2
		WHEN P.DepName = 'A7_GCD-03' THEN 3
		WHEN P.DepName = 'A7_GCD-04' THEN 4
		WHEN P.DepName = 'A7_GCD-05' THEN 5
        ELSE 99  
    END AS LeanOrder
FROM Production P
LEFT JOIN Defects D 
  ON P.WorkDate = D.WorkDate 
 AND P.DepNo     = D.DepNo	
LEFT JOIN TimeRange T 
  ON P.WorkDate = T.WorkDate 
 AND P.DepNo     = T.DepNo
LEFT JOIN TopDefectsAgg TD 
  ON P.WorkDate = TD.WorkDate 
 AND P.DepNo     = TD.DepNo 
ORDER BY P.WorkDate, LeanOrder;

 `
	err = db.Raw(query, req.StartDate, req.EndDate, req.Line).Scan(&Ex).Error
	if err != nil {
		log.Printf("Query execution error: %v", err)
		return nil, err
	}
	return Ex, nil
}
