package types

type TotalErrleanindayRequest struct {
	StartDate string `json:"StartDate"`
	EndDate   string `json:"EndDate"`
	DepName   string `json:"DepName"`
}

type TotalErrleaninday struct {
	DepNo                  string  `json:"DepNo"`
	DepName                string  `json:"DepName"`
	LeanName               string  `json:"LeanName"`
	ScanDate               string  `json:"ScanDate"`
	Pro_Quantity           string  `json:"Pro_Quantity"`
	Total_Defects          int     `json:"Total_Defects"`
	Defect_Rate_Percentage float64 `json:"Defect_Rate_Percentage"`
}

type DPPMErrleaninday struct {
	DepNo         string `json:"DepNo"`
	DepName       string `json:"DepName"`
	LeanName      string `json:"LeanName"`
	ScanDate      string `json:"ScanDate"`
	// Pro_Quantity  string `json:"Pro_Quantity"`
	Total_Defects int    `json:"Total_Defects"`
	DPPM          int    `json:"DPPM"`
}

type DPPMErrAllLeanInDay struct {
	DepNo         string `json:"DepNo"`
	DepName       string `json:"DepName"`
	LeanName      string `json:"LeanName"`
	TimeRange     string `json:"TimeRange"`
	Pro_Quantity  string `json:"Pro_Quantity"`
	Total_Defects int    `json:"Total_Defects"`
	DPPM          int    `json:"DPPM"`
}

type ItemErrleaninday struct {
	DepNo                  string  `json:"DepNo"`
	DepName                string  `json:"DepName"`
	ScanDate               string  `json:"ScanDate"`
	Pro_Quantity           string  `json:"Pro_Quantity"`
	Total_Defects          int     `json:"Total_Defects"`
	Defect_Rate_Percentage float64 `json:"Defect_Rate_Percentage"`
}

type Daily_Defect_DPPM_Report struct {
	DepNo         string `json:"DepNo"`
	DepName       string `json:"DepName"`
	LeanName      string `json:"LeanName"`
	YWSM          string `json:"YWSM"`
	ZWSM          string `json:"ZWSM"`
	Total_Defects int    `json:"Total_Defects"`
	TotalCTS      int    `json:"TotalCTS"`
	DPPM          int    `json:"DPPM"`
}
