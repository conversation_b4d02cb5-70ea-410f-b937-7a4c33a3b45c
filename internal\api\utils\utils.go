// utils/utils.go
package utils

import (
    "os"
    "time"

    "github.com/dgrijalva/jwt-go"
)

// <PERSON>h<PERSON>a bí mật để ký token
var jwtSecret = []byte(os.Getenv("SECRET_KEY"))

// Hàm tạo JWT token
func GenerateJWT(USERID int, GSBH string, Username string) (string, error) {
	claims := jwt.MapClaims{
		"GSBH":     GSBH,
		"USERID":   USERID,
		"USERNAME": Username,
		"exp":      time.Now().Add(time.Hour * 2).Unix(), // Hạn token 2 giờ
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}
