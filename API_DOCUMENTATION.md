# QCNB API Documentation

## Base URL

```
/api/v1
```

---

## 1. Common

### `GET /api/v1/ping`
- **Purpose:** Health check.
- **Request:** None
- **Response:**
```json
{
  "code": 200,
  "data": null,
  "message": "ok"
}
```

---

## 2. NBQC (Production Defect, Lean, Excel)

### `POST /api/v1/getProductionDefectSummary`
- **Purpose:** Get production defect summary by department and date range.
- **Request:**
```json
{
  "DepName": "string",
  "StartDate": "YYYY-MM-DD",
  "EndDate": "YYYY-MM-DD"
}
```
- **Response:** List of `ProductionDefectSummary` (see Models section).

### `GET /api/v1/getDepName`
- **Purpose:** Get department list.
- **Request:** None
- **Response:** List of `BDepartment`.

### `POST /api/v1/GetLeanProcessDepartments`
- **Purpose:** Get Lean process departments by department/date.
- **Request:**
```json
{
  "DepName": "string",
  "GXLB": "string",
  "Date": "YYYY-MM-DD"
}
```
- **Response:** List of `BDepartment`.

### `POST /api/v1/xuatexcel`
- **Purpose:** Export defect data to Excel.
- **Request:**
```json
{
  "StartDate": "YYYY-MM-DD",
  "EndDate": "YYYY-MM-DD",
  "Line": "string"
}
```
- **Response:** List of `ExcelTypes`.

### `POST /api/v1/GetExcelData`
- **Purpose:** Get Excel data (customized).
- **Request:** Same as `/xuatexcel`.
- **Response:** List of `ExcelTypes`.

---

## 3. QC Report

### `POST /api/v1/getTotalErrlean`
- **Purpose:** Get total Lean errors by date/department.
- **Request:**
```json
{
  "StartDate": "YYYY-MM-DD",
  "EndDate": "YYYY-MM-DD",
  "DepName": "string"
}
```
- **Response:** List of `TotalErrleaninday`.

### `POST /api/v1/getDPPMErrlean`
- **Purpose:** Get DPPM errors by Lean/date.
- **Request:** `ProductionDefectSummaryRequest`
- **Response:** List of `DPPMErrleaninday`.

### `POST /api/v1/getDPPMErrAllLean`
- **Purpose:** Get DPPM errors for all Lean in a day.
- **Request:** `TotalErrleanindayRequest`
- **Response:** List of `DPPMErrAllLeanInDay`.

### `POST /api/v1/getDailyDefectDPPMReport`
- **Purpose:** Get daily defect DPPM report.
- **Request:** `TotalErrleanindayRequest`
- **Response:** List of `Daily_Defect_DPPM_Report`.

### `POST /api/v1/getDailyDPPMByLeanReport`
- **Purpose:** Get DPPM report by Lean/date.
- **Request:** `TotalErrleanindayRequest`
- **Response:** List of `DPPMErrleaninday`.

---

## 4. Defect Qty by Process

### `POST /api/v1/GetDefectByHour`
- **Purpose:** Get defect quantity by hour and process.
- **Request:**
```json
{
  "DepName": "string",
  "GXLB": "string",
  "Date": "YYYY-MM-DD"
}
```
- **Response:** List of `GXLB_A_DefectByHour`.

---

## 5. Defect Qty by Line

### `POST /api/v1/GetDefectsQtyByLine`
- **Purpose:** Get defect quantity by line and hour.
- **Request:**
```json
{
  "GXLB": "string",
  "DepName": "string",
  "Date": "YYYY-MM-DD"
}
```
- **Response:** List of `HourlyDefectsByLean`.

---

## 6. Authentication

### `POST /api/v1/Login`
- **Purpose:** Login, returns user info and token.
- **Request:**
```json
{
  "GSBH": "string",
  "USERID": "string",
  "USERNAME": "string",
  "PWD": "string"
}
```
- **Response:** User info and token.

---

# Models

## ProductionDefectSummaryRequest
```go
{
  DepName: string,
  StartDate: string,
  EndDate: string
}
```

## ProductionDefectSummary
```go
{
  DepNo: string,
  LeanName: string,
  TimeRange: string,
  Pro_Quantity: int,
  Total_Defects: int,
  Defect_Rate: float64,
  data: [DefectAnalysis]
}
```

## DefectAnalysis
```go
{
  DepNo: string,
  YWSM: string,
  ZWSM: string,
  Total_Defects: int,
  Defect_Percentage: float64,
  Cumulative_Percentage: float64,
  Item: int
}
```

## BDepartment
```go
{
  DepName: string,
  GXLB: string,
  Date: string
}
```

## GXLB_A_DefectByHour
```go
{
  GXLB: string,
  CongDoan: string,
  YWSM: string,
  ZWSM: string,
  USERDATE: string,
  Total_Defects: int
}
```

## HourlyDefectsByLean
```go
{
  GXLB: string,
  YWSM: string,
  ZWSM: string,
  DepName: string,
  USERDATE: string,
  Total_Defects: int
}
```

## HourlyDefects
```go
{
  GXLB: string,
  DepName: string,
  Date: string
}
```

## TotalErrleanindayRequest
```go
{
  StartDate: string,
  EndDate: string,
  DepName: string
}
```

## TotalErrleaninday
```go
{
  DepNo: string,
  DepName: string,
  LeanName: string,
  ScanDate: string,
  Pro_Quantity: string,
  Total_Defects: int,
  Defect_Rate_Percentage: float64
}
```

## DPPMErrleaninday
```go
{
  DepNo: string,
  DepName: string,
  LeanName: string,
  ScanDate: string,
  Total_Defects: int,
  DPPM: int
}
```

## DPPMErrAllLeanInDay
```go
{
  DepNo: string,
  DepName: string,
  LeanName: string,
  TimeRange: string,
  Pro_Quantity: string,
  Total_Defects: int,
  DPPM: int
}
```

## Daily_Defect_DPPM_Report
```go
{
  DepNo: string,
  DepName: string,
  LeanName: string,
  YWSM: string,
  ZWSM: string,
  Total_Defects: int,
  TotalCTS: int,
  DPPM: int
}
```

## User_Login
```go
{
  GSBH: string,
  USERID: string,
  USERNAME: string,
  PWD: string
}
```

## ExcelTypes
```go
{
  WorkDate: string,
  DepNo: string,
  LeanName: string,
  TimeRange: string,
  Pro_Quantity: int,
  Total_Defects: int,
  Defect_Rate: float64,
  YWSM: string,
  ZWSM: string,
  LeanOrder: int
}
```

## ExcelRes
```go
{
  StartDate: string,
  EndDate: string,
  Line: string
}
```

## Login
```go
{
  UserId: string,
  UserName: string,
  Password: string,
  Role: string,
  Token: string
}
```

---

# Error Response Example

```json
{
  "code": 400,
  "data": null,
  "message": "Error message"
}
``` 